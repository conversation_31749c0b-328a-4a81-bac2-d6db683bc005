from langchain_core.documents import Document

from src.config import appConfig
from src.retriever.hybrid import build_bm25_retriever
from src.retriever.multi_vector import get_multi_vector_retriever
from src.retriever.pipeline import RetrieverPipeline
from src.vector.milvus_store import get_milvus_store


def init_retriever(
    collection_name: str, docs: list[Document] | None = None
) -> RetrieverPipeline:
    """根据配置初始化 RetrieverPipeline"""
    vectorstore = get_milvus_store(collection_name)
    multi_vector = get_multi_vector_retriever(vectorstore)

    retrievers = [multi_vector]
    weights = [appConfig.retriever.vector_weight]

    if appConfig.retriever.use_bm25 and docs:
        bm25 = build_bm25_retriever(docs)
        retrievers.append(bm25)
        weights.append(appConfig.retriever.bm25_weight)

    return RetrieverPipeline(
        retrievers=retrievers,
        weights=weights,
        rerank_model=appConfig.retriever.rerank_model,
        rerank_top_k=appConfig.retriever.rerank_top_k,
    )
