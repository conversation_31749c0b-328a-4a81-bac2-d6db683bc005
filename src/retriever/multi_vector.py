from langchain.retrievers import MultiVectorRetriever
from langchain.docstore import InMemoryDocstore
from langchain.storage import InMemoryByteStore
from langchain_core.vectorstores import VectorStore


def get_multi_vector_retriever(vectorstore: VectorStore) -> MultiVectorRetriever:
    """获取多向量检索器"""
    return MultiVectorRetriever(
        vectorstore=vectorstore,
        byte_store=InMemoryByteStore(),
        docstore=InMemoryDocstore()
    )
