from typing import List, Optional, Any

from langchain.retrievers import EnsembleRetriever
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever
from sentence_transformers import CrossEncoder


class RetrieverPipeline(BaseRetriever):
    """
    通用检索管道：
    - 支持多个 retriever 融合
    - 支持 CrossEncoder reranker 精排
    """

    # ---- Pydantic 字段声明 ----
    retrievers: List[BaseRetriever]
    weights: Optional[List[float]] = None
    rerank_model: Optional[str] = None
    rerank_top_k: int = 5

    # 这些字段在 __init__ 里会初始化
    ensemble: Optional[EnsembleRetriever] = None
    reranker: Optional[Any] = None  # CrossEncoder 没有 pydantic 支持，用 Any

    def __init__(self, **data):
        """
        注意：BaseRetriever 在新版 LangChain 中是 Pydantic 模型，
        所以要用 super().__init__(**data) 来触发校验。
        """
        super().__init__(**data)

        # 初始化 ensemble retriever
        self.ensemble = EnsembleRetriever(
            retrievers=self.retrievers, weights=self.weights
        )

        # 初始化 reranker
        self.reranker = CrossEncoder(self.rerank_model) if self.rerank_model else None

    def _get_relevant_documents(self, query: str) -> List[Document]:
        # 1. 多检索器融合召回
        candidates = self.ensemble.get_relevant_documents(query)

        # 2. reranker 精排
        if self.reranker:
            pairs = [(query, doc.page_content) for doc in candidates]
            scores = self.reranker.predict(pairs)
            scored_docs = list(zip(candidates, scores))
            scored_docs.sort(key=lambda x: x[1], reverse=True)
            return [doc for doc, _ in scored_docs[: self.rerank_top_k]]

        return candidates
