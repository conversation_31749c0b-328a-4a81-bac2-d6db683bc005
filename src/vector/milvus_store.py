from langchain_core.vectorstores import VectorStore
from langchain_milvus import <PERSON>lvus

from src.config import appConfig
from src.embedding.embedding_client import get_embedding


def get_milvus_store(collection_name: str) -> VectorStore:
    """根据 collection name 获取 Milvus VectorStore"""
    embedding = get_embedding()
    return Milvus(
        embedding_function=embedding,
        connection_args={
            "uri": appConfig.vector.uri,
            "user": appConfig.vector.user,
            "password": appConfig.vector.password,
            "db_name": appConfig.vector.db_name,
            "token": appConfig.vector.token,
            "timeout": appConfig.vector.timeout,
        },
        collection_name=collection_name,
    )
