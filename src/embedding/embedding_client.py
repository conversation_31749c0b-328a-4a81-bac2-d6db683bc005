from typing import Any, Union

from langchain.embeddings import init_embeddings
from langchain_core.embeddings import Embeddings
from langchain_core.runnables import Runnable

from src.config import appConfig


def get_embedding() -> Union[Embeddings, Runnable[Any, list[float]]]:
    """获取 Embedding 客户端"""
    return init_embeddings(
        provider=appConfig.embedding.provider, model=appConfig.embedding.model_name
    )
