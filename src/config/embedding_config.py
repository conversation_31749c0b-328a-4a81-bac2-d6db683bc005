from pydantic import BaseModel, Field

from .base import TOML_CONFIG


class EmbeddingConfig(BaseModel):
    """Embedding 配置"""

    provider: str = Field(
        default=TOML_CONFIG.get("embedding", {}).get("provider", ""),
        description="Embedding 提供商",
    )

    model_name: str = Field(
        default=TOML_CONFIG.get("embedding", {}).get("model_name", ""),
        description="Embedding 模型名称",
    )
