import unittest

from langchain_core.documents import Document
from pymilvus import DataType, FieldSchema, CollectionSchema

from src.vector.milvus_client import get_milvus_client
from src.vector.milvus_store import get_milvus_store


class TestMilvusStore(unittest.TestCase):
    """测试 MilvusStore"""

    def setUp(self):
        self.test_collection_name = "test_milvus_store_collection"
        self.test_dim = 768
        self.test_metric_type = "IP"
        self.milvus_client = get_milvus_client()
        self.schema = CollectionSchema(
            fields=[
                FieldSchema(
                    name="id", dtype=DataType.INT64, is_primary=True, auto_id=True
                ),
                FieldSchema(
                    name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.test_dim
                ),
                FieldSchema(
                    name="text", dtype=DataType.VARCHAR, max_length=65535
                )],
            description="test schema",
            enable_dynamic_field=True,
        )

        # 先删除集合，再创建集合，保证数据集干净
        if self.milvus_client.has_collection(self.test_collection_name):
            self.milvus_client.drop_collection(self.test_collection_name)
        self.milvus_client.create_collection(
            collection_name=self.test_collection_name,
            dimension=self.test_dim,
            metric_type=self.test_metric_type,
            schema=self.schema,
            auto_id=True,
        )

    def test_insert_and_search(self):
        """测试插入数据并搜索"""
        milvus_store = get_milvus_store(self.test_collection_name)

        # 插入数据
        docs = [
            Document(page_content="LangChain 是一个用于构建LLM应用的框架"),
            Document(page_content="Milvus 是一个开源向量数据库"),
        ]
        milvus_store.add_documents(docs)

        # flush & load
        self.milvus_client.flush(collection_name=self.test_collection_name)
        self.milvus_client.load_collection(collection_name=self.test_collection_name)

        # 检索
        res = milvus_store.similarity_search("什么是向量数据库？", k=1)
        print(res)
