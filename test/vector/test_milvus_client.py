from src.vector.milvus_client import get_milvus_client

test_collection_name = "test_collection"
test_dim = 128
test_metric_type = "IP"


def test_milvus_client():
    """测试 milvus 向量客户端连接"""
    milvus_client = get_milvus_client()
    collections = milvus_client.list_collections()
    print(collections)


def test_create_collection():
    """测试创建集合"""
    milvus_client = get_milvus_client()
    if not milvus_client.has_collection(test_collection_name):
        milvus_client.create_collection(collection_name=test_collection_name, dimension=test_dim, meric_type=test_metric_type)
    collections = milvus_client.list_collections()
    print(collections)
    assert test_collection_name in collections


def test_drop_collection():
    """测试删除集合"""
    milvus_client = get_milvus_client()
    if milvus_client.has_collection(test_collection_name):
        milvus_client.drop_collection(test_collection_name)
    collections = milvus_client.list_collections()
    print(collections)
    assert test_collection_name not in collections


def test_insert_and_search():
    """测试插入数据并搜索"""
    milvus_client = get_milvus_client()
    if not milvus_client.has_collection(test_collection_name):
        milvus_client.create_collection(collection_name=test_collection_name, dimension=test_dim, meric_type=test_metric_type)

    # 插入数据
    data = [
        {"id": i, "vector": [float(i)] * test_dim}
        for i in range(10)
    ]
    milvus_client.insert(test_collection_name, data)

    # flush & load
    milvus_client.flush(collection_name=test_collection_name)
    milvus_client.load_collection(collection_name=test_collection_name)

    # 检索
    query = [[0.0] * test_dim]
    res = milvus_client.search(
        collection_name=test_collection_name,
        data=query,
        output_fields=["id"],
        limit=3
    )
    print(res)
