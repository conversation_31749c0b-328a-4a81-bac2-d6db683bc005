import unittest

from langchain_core.documents import Document
from pymilvus import CollectionSchema, FieldSchema, DataType

from src.retriever.factory import init_retriever
from src.vector.milvus_client import get_milvus_client
from src.vector.milvus_store import get_milvus_store


class TestMilvusRetriever(unittest.TestCase):
    """基于电影表结构的端到端检索测试"""

    def setUp(self):
        self.collection_name = "test_movies_collection"
        self.dim = 768
        self.metric_type = "IP"
        self.client = get_milvus_client()

        # 清理旧集合
        if self.client.has_collection(self.collection_name):
            self.client.drop_collection(self.collection_name)

        # 定义 schema
        self.schema = CollectionSchema(
            fields=[
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.dim),
                FieldSchema(name="year", dtype=DataType.INT64),
                FieldSchema(name="score", dtype=DataType.FLOAT),
            ],
            description="test schema",
            enable_dynamic_field=True,
        )

        # 创建新集合
        self.client.create_collection(
            collection_name=self.collection_name,
            dimension=self.dim,
            metric_type=self.metric_type,
            schema=self.schema,
            auto_id=True,
        )

        # 初始化 vector store
        self.vector_store = get_milvus_store(self.collection_name)

        # 测试数据
        movies = [
            {"id": 1, "title": "霸王别姬", "actors": "张国荣, 张丰毅, 巩俐", "year": 1993, "score": 9.6,
             "description": "京剧伶人程蝶衣和段小楼的半生故事"},
            {"id": 2, "title": "阿凡达", "actors": "萨姆·沃辛顿, 佐伊·索尔达娜", "year": 2009, "score": 9.0,
             "description": "外星纳美人与人类冲突"},
            {"id": 3, "title": "泰坦尼克号", "actors": "莱昂纳多, 凯特·温斯莱特", "year": 1997, "score": 9.4,
             "description": "邮轮沉没的爱情故事"},
            {"id": 4, "title": "大话西游", "actors": "周星驰, 朱茵, 吴孟达", "year": 1995, "score": 9.2,
             "description": "经典喜剧爱情神话"},
            {"id": 5, "title": "星际穿越", "actors": "马修·麦康纳, 安妮·海瑟薇", "year": 2014, "score": 9.3,
             "description": "穿越黑洞的科幻故事"},
        ]
        docs = []
        for m in movies:
            docs.append(Document(page_content=m["title"],
                                 metadata={"field": "title", "movie_id": m["id"], "year": m["year"],
                                           "score": m["score"]}))
            docs.append(Document(page_content=m["actors"],
                                 metadata={"field": "actors", "movie_id": m["id"], "year": m["year"],
                                           "score": m["score"]}))
            docs.append(Document(page_content=m["description"],
                                 metadata={"field": "description", "movie_id": m["id"], "year": m["year"],
                                           "score": m["score"]}))
        self.vector_store.add_documents(docs)

        # flush & load
        self.client.flush(collection_name=self.collection_name)
        self.client.load_collection(collection_name=self.collection_name)

        # 初始化 retriever pipeline
        self.retriever = init_retriever(self.collection_name, docs)

    def test_search_actor(self):
        """测试搜索演员"""
        res = self.retriever.get_relevant_documents("张国荣的电影")
        print(res)
